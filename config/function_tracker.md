# Function Tracker Configuration

The Function Tracker now supports enhanced response payload logging with configurable options.

## Configuration Options

Add these options to your `config/config.exs` or environment-specific config files:

```elixir
config :service_manager, :function_tracker,
  # Response size limit in bytes (default: 10,000)
  response_size_limit: 10_000,
  
  # Enable/disable response data redaction (default: true)
  enable_response_redaction: true,
  
  # Enable/disable enhanced response parsing (default: true)
  enable_response_parsing: true,
  
  # Additional sensitive field patterns for redaction
  additional_sensitive_fields: ["custom_secret", "internal_token"]
```

## Features

### Enhanced Response Body Processing
- **JSON Parsing**: Automatic JSON parsing with error handling
- **XML Detection**: Identifies and handles XML responses
- **Content Type Detection**: Uses Content-Type headers and content analysis
- **Binary Data Handling**: Safe handling of binary responses with Base64 preview

### Response Data Redaction
- **Automatic Redaction**: Removes sensitive data from response logs
- **Configurable Patterns**: Default patterns for common sensitive fields
- **Custom Fields**: Add your own sensitive field patterns
- **Header Redaction**: Extends existing header redaction to response bodies

### Size Limiting
- **Configurable Limits**: Set maximum response body size for logging
- **Graceful Truncation**: Large responses are truncated with preview
- **Size Metadata**: Response size information in logs
- **Memory Protection**: Prevents memory issues with large payloads

### Response Metadata
- **Content Type**: Logs response Content-Type header
- **Response Size**: Human-readable size formatting (bytes, KB, MB)
- **Processing Flags**: Shows which features are enabled
- **Timestamps**: Response processing timestamps

## Default Sensitive Fields

The following fields are automatically redacted from response data:
- password, token, secret, key, authorization, auth
- pin, otp, ssn, social_security
- credit_card, card_number, cvv, cvc
- account_number, routing_number, iban
- private_key, api_key, access_token, refresh_token
- session_id, csrf_token, x-csrf-token

## Log Format Changes

### Response Section Enhancement
```
└─ Response (200 in 150ms):
   ├─ Metadata: content-type: application/json, size: 2.5 KB, 🔒 redacted, 📝 parsed
   ├─ Response Data (JSON):
   │  ├─ Account Data:
   │  │  ├─ cleared_balance: 1,500.00 ZMW
   │  │  └─ available_balance: 1,200.00 ZMW
   └─ Security Headers:
      └─ strict-transport-security: max-age=********
```

### Database Fields
- `response_info`: Enhanced with metadata, size info, and processing flags
- `result`: Continues to store full function return value
- Backward compatibility maintained

## Migration Notes

- All existing functionality is preserved
- New features are opt-in via configuration
- Default settings maintain current behavior
- No database schema changes required
