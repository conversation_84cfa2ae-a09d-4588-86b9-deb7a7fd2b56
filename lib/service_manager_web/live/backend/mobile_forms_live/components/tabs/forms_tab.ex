defmodule ServiceManagerWeb.Backend.MobileFormsLive.Components.Tabs.FormsTab do
  use Phoenix.Component
  import ServiceManagerWeb.CoreComponents
  alias ServiceManagerWeb.Backend.MobileFormsLive.Components.Shared.FieldComponents
  alias ServiceManagerWeb.Backend.MobileFormsLive.Components.Shared.MobilePreview

  ## Forms Tab Component

  attr :pages, :list, default: []
  attr :forms, :list, default: []
  attr :selected_page_id, :any
  attr :selected_form_id, :any
  attr :submit_to, :string
  attr :selected_screen_id, :any
  attr :fields, :list, default: []
  attr :screens, :list, default: []
  attr :all_pages, :list, default: []
  attr :all_forms, :list, default: []
  attr :editing_field_id, :any, default: nil
  attr :editing_form_id, :any, default: nil
  attr :dynamic_routes, :list, default: []
  attr :form_route_id, :string, default: nil
  attr :database_tables, :list, default: []
  def forms_tab(assigns) do
    ~H"""
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div>
        <div class="text-xs text-gray-600 mb-2 flex items-center">
          <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          Forms Management
        </div>

        <!-- Screen Selection for Filtering -->
        <form phx-change="v2_filter_by_screen" class="mb-3">
          <label class="block text-xs font-medium text-gray-600 mb-1">Filter by Screen:</label>
          <select 
            name="value"
            class="w-full border rounded px-2 py-1 text-xs bg-white"
          >
            <option value="">🏠 All Screens</option>
            <%= for screen <- @screens do %>
              <option value={screen.id} selected={@selected_screen_id == screen.id}>
                🏠 <%= screen.name %>
              </option>
            <% end %>
          </select>
        </form>
        
        <form phx-submit="v2_create_form" phx-change="v2_filter_by_page" class="mb-3 space-y-2">
          <div class="flex gap-2 items-center">
            <input type="text" name="v2_form[name]" placeholder="Form name" required class="border rounded px-2 py-1 text-xs min-w-0 flex-1" />
            <select name="v2_form[page_id]" required class="border rounded px-2 py-1 text-xs min-w-0 flex-1 bg-white">
              <option value="">📄 Select Page (filters forms below)</option>
              <%= if @selected_screen_id do %>
                <!-- Show pages for selected screen -->
                <% screen_pages = Enum.filter(@all_pages || [], &(&1.screen_id == @selected_screen_id)) %>
                <%= for page <- screen_pages do %>
                  <option value={page.id} selected={@selected_page_id == page.id}>
                    📄 <%= page.name %>
                  </option>
                <% end %>
              <% else %>
                <!-- Show all pages with screen context -->
                <%= for page <- @all_pages || [] do %>
                  <option value={page.id} selected={@selected_page_id == page.id}>
                    📄 <%= page.name %>
                    <%= if page.screen_id do %>
                      <%= case Enum.find(@screens || [], &(&1.id == page.screen_id)) do %>
                        <% %{name: screen_name} -> %>
                          (in <%= screen_name %>)
                        <% _ -> %>
                          
                      <% end %>
                    <% end %>
                  </option>
                <% end %>
              <% end %>
            </select>
            <button type="submit" class="text-xs px-2 py-1 bg-indigo-600 text-white rounded whitespace-nowrap" disabled={
              if @selected_screen_id do
                Enum.filter(@all_pages || [], &(&1.screen_id == @selected_screen_id)) == []
              else
                (@all_pages || []) == []
              end
            }>
              Create Form
            </button>
          </div>
          <div class="flex items-center space-x-4">
            <label class="flex items-center text-xs">
              <input type="checkbox" name="v2_form[active]" class="mr-1" checked />
              Enabled
            </label>
            <label class="flex items-center text-xs">
              <input type="checkbox" name="v2_form[app_view]" class="mr-1" checked />
              App View
            </label>
          </div>
          <%= if (@selected_screen_id && Enum.filter(@all_pages || [], &(&1.screen_id == @selected_screen_id)) == []) || (!@selected_screen_id && (@all_pages || []) == []) do %>
            <div class="text-xs text-amber-600 bg-amber-50 border border-amber-200 rounded px-2 py-1">
              <%= if @selected_screen_id do %>
                Create a page for this screen first before adding forms
              <% else %>
                Create a page first before adding forms
              <% end %>
            </div>
          <% end %>
        </form>

        <!-- Form Edit Section -->
        <%= if assigns[:editing_form_id] do %>
          <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <h4 class="text-sm font-medium text-gray-700 flex items-center">
                <svg class="w-4 h-4 mr-1 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit Form
              </h4>
              <button 
                phx-click="v2_cancel_edit_form" 
                class="text-gray-500 hover:text-gray-700 p-1"
                title="Cancel editing"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
              </button>
            </div>
            
            <.form_edit_section editing_form_id={@editing_form_id} all_forms={@all_forms} all_pages={@all_pages} />
          </div>
        <% end %>

        <!-- Forms List Section -->
        <div class="mt-4">
          <h4 class="text-sm font-medium text-gray-700 mb-2 flex items-center">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Forms List
            <%= if @selected_page_id do %>
              <%= case Enum.find(@all_pages || [], &(&1.id == @selected_page_id)) do %>
                <% %{name: page_name} -> %>
                  <span class="text-xs text-gray-500 font-normal ml-1">(in <%= page_name %>)</span>
                <% _ -> %>
                  
              <% end %>
            <% end %>
          </h4>
          
          <div class="space-y-2 max-h-64 overflow-y-auto">
            <%= cond do %>
              <% @selected_page_id && (@all_forms || []) != [] -> %>
                <!-- Show forms for selected page -->
                <% page_forms = Enum.filter(@all_forms || [], &(&1.page_id == @selected_page_id)) %>
                <%= if page_forms != [] do %>
                  <%= for form <- page_forms do %>
                    <div class={[
                      "flex items-center justify-between p-2 border rounded text-xs cursor-pointer transition-colors",
                      if(@selected_form_id == form.id, do: "bg-blue-50 border-blue-200", else: "bg-gray-50 border-gray-200 hover:bg-gray-100")
                    ]} phx-click="v2_select_form" phx-value-id={form.id}>
                      <div class="flex-1">
                        <div class="flex items-center gap-2">
                          <div class="font-medium">📝 <%= form.name %></div>
                          <div class="flex items-center gap-1">
                            <%= if form.active do %>
                              <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                ✓ Enabled
                              </span>
                            <% else %>
                              <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                ✗ Disabled
                              </span>
                            <% end %>
                            <%= if Map.get(form, :app_view, true) do %>
                              <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                📱 App
                              </span>
                            <% end %>
                          </div>
                        </div>
                        <%= case Enum.find(@all_pages || [], &(&1.id == form.page_id)) do %>
                          <% %{name: page_name} -> %>
                            <div class="text-gray-500">in <%= page_name %></div>
                          <% _ -> %>
                            <div class="text-gray-400">Unknown page</div>
                        <% end %>
                      </div>
                      <div class="flex items-center space-x-1">
                        <button 
                          phx-click="v2_edit_form" 
                          phx-value-id={form.id}
                          class="text-indigo-500 hover:text-indigo-700 p-1"
                          title="Edit form details"
                        >
                          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                          </svg>
                        </button>
                        <button 
                          phx-click="v2_toggle_form_active" 
                          phx-value-id={form.id} 
                          phx-value-active={!form.active}
                          class="text-gray-500 hover:text-gray-700 p-1"
                          title={if form.active, do: "Disable form", else: "Enable form"}
                        >
                          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <%= if form.active do %>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464m1.414 1.414l-7.071 7.071m7.071-7.071l4.242 4.242M8.464 8.464L8 8m.464.464L20 20"></path>
                            <% else %>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            <% end %>
                          </svg>
                        </button>
                        <button 
                          phx-click="v2_toggle_form_app_view" 
                          phx-value-id={form.id} 
                          phx-value-app-view={!Map.get(form, :app_view, true)}
                          class="text-blue-500 hover:text-blue-700 p-1"
                          title={if Map.get(form, :app_view, true), do: "Remove from app view", else: "Show in app view"}
                        >
                          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <%= if Map.get(form, :app_view, true) do %>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"></path>
                            <% else %>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                              <line x1="5" y1="5" x2="19" y2="19" stroke="currentColor" stroke-width="2"/>
                            <% end %>
                          </svg>
                        </button>
                        <button phx-click="v2_delete_form" phx-value-id={form.id} data-confirm="Delete this form?" class="text-red-500 hover:text-red-700 p-1">
                          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                          </svg>
                        </button>
                      </div>
                    </div>
                  <% end %>
                <% else %>
                  <div class="p-4 text-center text-gray-400 text-xs">
                    No forms found for this page. Create a form above to get started.
                  </div>
                <% end %>
                
              <% @selected_screen_id && (@all_forms || []) != [] -> %>
                <!-- Show forms for selected screen with page context -->
                <% filtered_forms = Enum.filter(@all_forms || [], fn f ->
                  page = Enum.find(@all_pages || [], &(&1.id == f.page_id))
                  page && page.screen_id == @selected_screen_id
                end) %>
                <%= if filtered_forms != [] do %>
                  <%= for form <- filtered_forms do %>
                    <div class={[
                      "flex items-center justify-between p-2 border rounded text-xs cursor-pointer transition-colors",
                      if(@selected_form_id == form.id, do: "bg-blue-50 border-blue-200", else: "bg-gray-50 border-gray-200 hover:bg-gray-100")
                    ]} phx-click="v2_select_form" phx-value-id={form.id}>
                      <div class="flex-1">
                        <div class="flex items-center gap-2">
                          <div class="font-medium">📝 <%= form.name %></div>
                          <div class="flex items-center gap-1">
                            <%= if form.active do %>
                              <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                ✓ Enabled
                              </span>
                            <% else %>
                              <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                                ✗ Disabled
                              </span>
                            <% end %>
                            <%= if Map.get(form, :app_view, true) do %>
                              <span class="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                📱 App
                              </span>
                            <% end %>
                          </div>
                        </div>
                        <%= case Enum.find(@all_pages || [], &(&1.id == form.page_id)) do %>
                          <% %{name: page_name} -> %>
                            <div class="text-gray-500">in <%= page_name %></div>
                          <% _ -> %>
                            <div class="text-gray-400">Unknown page</div>
                        <% end %>
                      </div>
                      <div class="flex items-center space-x-1">
                        <button 
                          phx-click="v2_edit_form" 
                          phx-value-id={form.id}
                          class="text-indigo-500 hover:text-indigo-700 p-1"
                          title="Edit form details"
                        >
                          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                          </svg>
                        </button>
                        <button 
                          phx-click="v2_toggle_form_active" 
                          phx-value-id={form.id} 
                          phx-value-active={!form.active}
                          class="text-gray-500 hover:text-gray-700 p-1"
                          title={if form.active, do: "Disable form", else: "Enable form"}
                        >
                          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <%= if form.active do %>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464m1.414 1.414l-7.071 7.071m7.071-7.071l4.242 4.242M8.464 8.464L8 8m.464.464L20 20"></path>
                            <% else %>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            <% end %>
                          </svg>
                        </button>
                        <button 
                          phx-click="v2_toggle_form_app_view" 
                          phx-value-id={form.id} 
                          phx-value-app-view={!Map.get(form, :app_view, true)}
                          class="text-blue-500 hover:text-blue-700 p-1"
                          title={if Map.get(form, :app_view, true), do: "Remove from app view", else: "Show in app view"}
                        >
                          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <%= if Map.get(form, :app_view, true) do %>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z"></path>
                            <% else %>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                              <line x1="5" y1="5" x2="19" y2="19" stroke="currentColor" stroke-width="2"/>
                            <% end %>
                          </svg>
                        </button>
                        <button phx-click="v2_delete_form" phx-value-id={form.id} data-confirm="Delete this form?" class="text-red-500 hover:text-red-700 p-1">
                          <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                          </svg>
                        </button>
                      </div>
                    </div>
                  <% end %>
                <% else %>
                  <div class="p-4 text-center text-gray-400 text-xs">
                    No forms found for this screen. Create a form above to get started.
                  </div>
                <% end %>
                
              <% !@selected_screen_id && !@selected_page_id && (@all_forms || []) != [] -> %>
                <!-- Show all forms with full context -->
                <%= for form <- @all_forms || [] do %>
                  <div class={[
                    "flex items-center justify-between p-2 border rounded text-xs cursor-pointer transition-colors",
                    if(@selected_form_id == form.id, do: "bg-blue-50 border-blue-200", else: "bg-gray-50 border-gray-200 hover:bg-gray-100")
                  ]} phx-click="v2_select_form" phx-value-id={form.id}>
                    <div class="flex-1">
                      <div class="font-medium">📝 <%= form.name %></div>
                      <%= case Enum.find(@all_pages || [], &(&1.id == form.page_id)) do %>
                        <% %{name: page_name, screen_id: screen_id} -> %>
                          <%= case Enum.find(@screens || [], &(&1.id == screen_id)) do %>
                            <% %{name: screen_name} -> %>
                              <div class="text-gray-500">in <%= page_name %> (🏠 <%= screen_name %>)</div>
                            <% _ -> %>
                              <div class="text-gray-500">in <%= page_name %></div>
                          <% end %>
                        <% _ -> %>
                          <div class="text-gray-400">Unknown page</div>
                      <% end %>
                    </div>
                    <div class="flex items-center space-x-1">
                      <button phx-click="v2_delete_form" phx-value-id={form.id} data-confirm="Delete this form?" class="text-red-500 hover:text-red-700 p-1">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                      </button>
                    </div>
                  </div>
                <% end %>
                
              <% true -> %>
                <!-- Empty state -->
                <div class="p-4 text-center text-gray-400 text-xs">
                  <%= cond do %>
                    <% @selected_page_id -> %>
                      No forms found for this page. Create a form above to get started.
                    <% @selected_screen_id -> %>
                      No forms found for this screen. Create a form above to get started.
                    <% (@all_forms || []) == [] -> %>
                      No forms created yet. Create your first form above to get started.
                    <% true -> %>
                      Select a screen or page to filter forms, or choose "All" to see everything.
                  <% end %>
                </div>
            <% end %>
          </div>
        </div>

        <!-- Form Settings Section -->
        <%= if @selected_form_id do %>
          <div class="mt-6 border-t pt-4">
            <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              Form Settings
            </h4>
            
            <.form_settings_section selected_form_id={@selected_form_id} forms={@forms} />
          </div>

          <!-- Form Submit To Section -->
          <div class="mt-6 border-t pt-4">
            <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
              </svg>
              Form Submission
            </h4>
            
            <.form_submit_to_section submit_to={@submit_to} route_id={@form_route_id || get_selected_form_route_id(@forms, @selected_form_id)} dynamic_routes={@dynamic_routes} />
          </div>

          <!-- Field Management Section -->
          <div class="mt-6 border-t pt-4">
            <h4 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
              Field Management
            </h4>
            
            <.field_creation_form database_tables={@database_tables} />
            <.field_list fields={@fields} editing_field_id={@editing_field_id} database_tables={@database_tables} />
          </div>
        <% end %>
      </div>
      <div>
        <%= MobilePreview.mobile_preview(%{
          level: (
            cond do
              @selected_page_id -> "forms"
              @selected_screen_id -> "pages"
              true -> "screens"
            end
          ),
          items: (
            cond do
              @selected_page_id ->
                Enum.filter(@all_forms || [], &(&1.page_id == @selected_page_id))
              @selected_screen_id ->
                Enum.filter(@all_pages || [], &(&1.screen_id == @selected_screen_id))
              true ->
                @screens || []
            end
          ),
          active_id: @selected_form_id,
          all_screens: @screens || [],
          selected_screen_id: @selected_screen_id,
          all_pages: @all_pages || @pages || [],
          selected_page_id: @selected_page_id,
          all_fields: @fields || [],
          tab_context: "forms"
        }) %>
      </div>
    </div>
    """
  end

  # Field creation form component
  attr :database_tables, :list, default: []
  defp field_creation_form(assigns) do
    ~H"""
    <form phx-submit="v2_create_field" class="mb-3 space-y-3 p-3 bg-gray-50 rounded-lg">
      <h5 class="text-xs font-semibold text-gray-700 mb-2">Add New Field</h5>

      <!-- Basic Field Info -->
      <div class="grid grid-cols-2 gap-2">
        <input type="text" name="v2_field[field_name]" placeholder="Field name (e.g., amount)" required class="border rounded px-2 py-1 text-xs" />
        <input type="text" name="v2_field[label]" placeholder="Display label" required class="border rounded px-2 py-1 text-xs" />
      </div>

      <!-- Field Type and Configuration -->
      <div class="grid grid-cols-2 gap-2">
        <select name="v2_field[field_type]" required class="border rounded px-2 py-1 text-xs bg-white" onchange="toggleFieldOptions(this.value)">
          <option value="">🔧 Select Field Type</option>
          <optgroup label="📝 Text Fields">
            <option value="string">Text Input</option>
            <option value="textarea">Text Area</option>
            <option value="email">Email</option>
            <option value="password">Password</option>
            <option value="phone">Phone Number</option>
          </optgroup>
          <optgroup label="🔢 Number Fields">
            <option value="number">Number (Decimal)</option>
            <option value="integer">Integer</option>
          </optgroup>
          <optgroup label="📅 Date Fields">
            <option value="date">Date</option>
            <option value="datetime">Date & Time</option>
          </optgroup>
          <optgroup label="📋 Selection Fields">
            <option value="select">Dropdown</option>
            <option value="multiselect">Multi-Select</option>
            <option value="boolean">Checkbox</option>
          </optgroup>
          <optgroup label="🔘 Action Fields">
            <option value="button">Button</option>
          </optgroup>
        </select>
        <input type="number" name="v2_field[field_order]" placeholder="Order" value="0" min="0" class="border rounded px-2 py-1 text-xs" />
      </div>

      <!-- Field Options (for select/multiselect) -->
      <div id="field-options" class="hidden">
        <label class="block text-xs font-medium text-gray-600 mb-1">Options (one per line):</label>
        <textarea name="v2_field[options]" placeholder="Option 1&#10;Option 2&#10;Option 3" rows="3" class="w-full border rounded px-2 py-1 text-xs"></textarea>
      </div>

      <!-- Data Source (for dropdown fields) -->
      <div id="data-source-options" class="hidden">
        <label class="block text-xs font-medium text-gray-600 mb-1">Data Source (Database Table):</label>
        <select name="v2_field[data_source]" class="w-full border rounded px-2 py-1 text-xs bg-white">
          <option value="">Choose a table</option>
          <%= for table <- @database_tables do %>
            <option value={table}><%= table %></option>
          <% end %>
        </select>
        <p class="text-xs text-gray-500 mt-1">Select a database table to populate dropdown options dynamically</p>
        
        <div class="mt-2">
          <label class="block text-xs font-medium text-gray-600 mb-1">Data Source Format:</label>
          <input name="v2_field[data_source_format]" type="text" placeholder="e.g., &#123;id&#125;:&#123;name&#125;" class="w-full border rounded px-2 py-1 text-xs" />
          <p class="text-xs text-gray-500 mt-1">Format for displaying data (e.g., &#123;id&#125;:&#123;name&#125; or &#123;value&#125;)</p>
        </div>
      </div>

      <!-- Field Settings -->
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <label class="flex items-center text-xs">
            <input type="checkbox" name="v2_field[is_required]" class="mr-1" />
            Required
          </label>
          <label class="flex items-center text-xs">
            <input type="checkbox" name="v2_field[active]" class="mr-1" checked />
            Active
          </label>
        </div>
        <button type="submit" class="text-xs px-3 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
          ➕ Add Field
        </button>
      </div>
    </form>

    <script>
      function toggleFieldOptions(fieldType) {
        const optionsDiv = document.getElementById('field-options');
        const dataSourceDiv = document.getElementById('data-source-options');
        
        // Hide both by default
        optionsDiv.classList.add('hidden');
        dataSourceDiv.classList.add('hidden');
        
        // Show appropriate section based on field type
        if (fieldType === 'select' || fieldType === 'multiselect') {
          optionsDiv.classList.remove('hidden');
          dataSourceDiv.classList.remove('hidden');
        }
      }
    </script>
    """
  end

  # Field list component
  attr :fields, :list, default: []
  attr :editing_field_id, :any
  attr :database_tables, :list, default: []
  defp field_list(assigns) do
    ~H"""
    <!-- Enhanced Fields List -->
    <div class="mt-4">
      <h5 class="text-xs font-semibold text-gray-700 mb-2 flex items-center">
        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
        </svg>
        Form Fields
        <span class="ml-2 text-gray-500 font-normal">(<%= length(@fields || []) %> fields)</span>
      </h5>

      <div class="space-y-2 max-h-64 overflow-y-auto">
        <%= for field <- Enum.sort_by(@fields || [], & &1.field_order) do %>
          <%= if @editing_field_id == field.id do %>
            <!-- Inline Edit Form -->
            <.field_edit_form field={field} database_tables={@database_tables} />
          <% else %>
            <!-- Normal Field Display with Drag & Drop -->
            <%= FieldComponents.draggable_field_row(%{
              field: field,
              editing_field_id: @editing_field_id
            }) %>
          <% end %>
        <% end %>

        <%= if (@fields || []) == [] do %>
          <div class="p-6 text-center text-gray-400 text-xs bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
            <svg class="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            <div class="font-medium">No fields added yet</div>
            <div class="text-gray-300 mt-1">Add your first field above to get started</div>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  # Field edit form component
  attr :field, :map, required: true
  attr :database_tables, :list, default: []
  defp field_edit_form(assigns) do
    ~H"""
    <!-- Inline Edit Form -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
      <form phx-submit="v2_update_field" phx-value-id={@field.id} class="space-y-3">
        <div class="flex items-center justify-between mb-2">
          <h6 class="text-sm font-medium text-blue-700">Editing Field</h6>
          <button
            type="button"
            phx-click="v2_cancel_edit_field"
            class="text-gray-500 hover:text-gray-700"
            title="Cancel editing"
          >
            ✕
          </button>
        </div>

        <!-- Edit Form Fields -->
        <div class="grid grid-cols-2 gap-2">
          <input type="text" name="field[field_name]" value={@field.field_name} placeholder="Field name" required class="border rounded px-2 py-1 text-xs" />
          <input type="text" name="field[label]" value={@field.label} placeholder="Display label" required class="border rounded px-2 py-1 text-xs" />
        </div>

        <div class="grid grid-cols-2 gap-2">
          <select name="field[field_type]" required class="border rounded px-2 py-1 text-xs bg-white">
            <option value="">🔧 Select Field Type</option>
            <optgroup label="📝 Text Fields">
              <option value="string" selected={@field.field_type == "string"}>Text Input</option>
              <option value="textarea" selected={@field.field_type == "textarea"}>Text Area</option>
              <option value="email" selected={@field.field_type == "email"}>Email</option>
              <option value="password" selected={@field.field_type == "password"}>Password</option>
              <option value="phone" selected={@field.field_type == "phone"}>Phone Number</option>
            </optgroup>
            <optgroup label="🔢 Number Fields">
              <option value="number" selected={@field.field_type == "number"}>Number (Decimal)</option>
              <option value="integer" selected={@field.field_type == "integer"}>Integer</option>
            </optgroup>
            <optgroup label="📅 Date Fields">
              <option value="date" selected={@field.field_type == "date"}>Date</option>
              <option value="datetime" selected={@field.field_type == "datetime"}>Date & Time</option>
            </optgroup>
            <optgroup label="📋 Selection Fields">
              <option value="select" selected={@field.field_type == "select"}>Dropdown</option>
              <option value="multiselect" selected={@field.field_type == "multiselect"}>Multi-Select</option>
              <option value="boolean" selected={@field.field_type == "boolean"}>Checkbox</option>
            </optgroup>
            <optgroup label="🔘 Action Fields">
              <option value="button" selected={@field.field_type == "button"}>Button</option>
            </optgroup>
          </select>
          <input type="number" name="field[field_order]" value={@field.field_order} placeholder="Order" min="0" class="border rounded px-2 py-1 text-xs" />
        </div>

        <!-- Field Options (for select/multiselect) -->
        <%= if @field.field_type in ["select", "multiselect"] do %>
          <div>
            <label class="block text-xs font-medium text-gray-600 mb-1">Options (one per line):</label>
            <textarea name="field[options]" placeholder="Option 1&#10;Option 2&#10;Option 3" rows="3" class="w-full border rounded px-2 py-1 text-xs"><%= @field.options || "" %></textarea>
          </div>
          
          <div>
            <label class="block text-xs font-medium text-gray-600 mb-1">Data Source (Database Table):</label>
            <select name="field[data_source]" class="w-full border rounded px-2 py-1 text-xs bg-white">
              <option value="">Choose a table</option>
              <%= for table <- @database_tables do %>
                <option value={table} selected={@field.data_source == table}><%= table %></option>
              <% end %>
            </select>
            <p class="text-xs text-gray-500 mt-1">Select a database table to populate dropdown options dynamically</p>
          </div>
          
          <div>
            <label class="block text-xs font-medium text-gray-600 mb-1">Data Source Format:</label>
            <input name="field[data_source_format]" type="text" value={@field.data_source_format || ""} placeholder="e.g., &#123;id&#125;:&#123;name&#125;" class="w-full border rounded px-2 py-1 text-xs" />
            <p class="text-xs text-gray-500 mt-1">Format for displaying data (e.g., &#123;id&#125;:&#123;name&#125; or &#123;value&#125;)</p>
          </div>
        <% end %>


        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <label class="flex items-center text-xs">
              <input type="checkbox" name="field[is_required]" checked={@field.is_required} class="mr-1" />
              Required
            </label>
            <label class="flex items-center text-xs">
              <input type="checkbox" name="field[active]" checked={@field.active} class="mr-1" />
              Active
            </label>
          </div>
          <div class="flex space-x-2">
            <button type="button" phx-click="v2_cancel_edit_field" class="text-xs px-2 py-1 bg-gray-500 text-white rounded hover:bg-gray-600">
              Cancel
            </button>
            <button type="submit" class="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700">
              Save Changes
            </button>
          </div>
        </div>
      </form>
    </div>
    """
  end

  # Form submit to section component
  attr :submit_to, :string
  attr :route_id, :string
  attr :dynamic_routes, :list, default: []
  defp form_submit_to_section(assigns) do
    ~H"""
    <div class="p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <h6 class="text-xs font-medium text-blue-900 mb-2">Form Submission Configuration</h6>
      
      <div class="space-y-3">
        <!-- Route Selection Dropdown -->
        <div>
          <label class="block text-xs font-medium text-gray-600 mb-1">Select Route:</label>
          <.simple_form
            for={%{}}
            id="route-selection-form"
            phx-change="v2_form_route_selected"
          >
            <select 
              id="route-select"
              name="route_selection" 
              class="w-full border rounded px-2 py-1 text-xs bg-white"
            >
              <option value="">Choose a route</option>
              <%= for route <- @dynamic_routes |> Enum.filter(&(&1.enabled)) |> Enum.sort_by(&(&1.name)) do %>
                <option value={route.id} selected={@route_id == route.id}>
                  <%= route.method %> <%= route.name %> (<%= route.path %>)
                </option>
              <% end %>
            </select>
          </.simple_form>
        </div>
        
        <!-- Current Submit To Display -->
        <%= if @submit_to && @submit_to != "" do %>
          <div class="p-2 bg-white border border-blue-300 rounded text-xs">
            <span class="font-medium text-gray-600">Current Submit URL:</span>
            <span class="text-blue-800 ml-1"><%= @submit_to %></span>
          </div>
        <% else %>
          <div class="p-2 bg-gray-100 border border-gray-300 rounded text-xs text-gray-500">
            No route selected
          </div>
        <% end %>
        
        <p class="text-xs text-blue-700">
          Select a route from the dropdown to set where this form should submit data.
        </p>
      </div>
    </div>
    """
  end

  # Form settings section component
  attr :selected_form_id, :string
  attr :forms, :list, default: []
  defp form_settings_section(assigns) do
    selected_form = Enum.find(assigns.forms, &(&1.id == assigns.selected_form_id))
    assigns = assign(assigns, :selected_form, selected_form)
    
    ~H"""
    <%= if @selected_form do %>
      <div class="p-3 bg-gray-50 border border-gray-200 rounded-lg">
        <h6 class="text-xs font-medium text-gray-700 mb-2">Form Configuration</h6>
        
        <.simple_form
          for={%{}}
          id="form-settings-form"
          phx-change="v2_update_form_settings"
        >
          <div class="space-y-3">
            <div class="flex items-center space-x-6">
              <label class="flex items-center text-xs">
                <input 
                  type="checkbox" 
                  name="active" 
                  checked={@selected_form.active}
                  phx-value-form-id={@selected_form.id}
                  class="mr-2 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" 
                />
                <span class="font-medium">Form Enabled</span>
              </label>
              <label class="flex items-center text-xs">
                <input 
                  type="checkbox" 
                  name="app_view" 
                  checked={Map.get(@selected_form, :app_view, true)}
                  phx-value-form-id={@selected_form.id}
                  class="mr-2 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" 
                />
                <span class="font-medium">Show in App View</span>
              </label>
            </div>
            
            <div class="flex items-center space-x-4">
              <label class="text-xs font-medium text-gray-700">Form Type:</label>
              <select 
                name="type" 
                phx-value-form-id={@selected_form.id}
                class="text-xs border rounded px-2 py-1 bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="form" selected={Map.get(@selected_form, :type) == "form" or Map.get(@selected_form, :type) == nil}>Form</option>
                <option value="confirmation" selected={Map.get(@selected_form, :type) == "confirmation"}>Confirmation</option>
                <option value="summary" selected={Map.get(@selected_form, :type) == "summary"}>Summary</option>
              </select>
            </div>
          </div>
        </.simple_form>
        
        <p class="text-xs text-gray-500 mt-2">
          Toggle these settings to control form visibility and availability.
        </p>
      </div>
    <% end %>
    """
  end

  # Helper function to get the route_id of the selected form
  defp get_selected_form_route_id(forms, selected_form_id) do
    case Enum.find(forms, &(&1.id == selected_form_id)) do
      nil -> nil
      form -> form.route_id
    end
  end

  # Form edit section component
  attr :editing_form_id, :string
  attr :all_forms, :list, default: []
  attr :all_pages, :list, default: []
  defp form_edit_section(assigns) do
    editing_form = Enum.find(assigns.all_forms, &(&1.id == assigns.editing_form_id))
    assigns = assign(assigns, :editing_form, editing_form)
    
    ~H"""
    <%= if @editing_form do %>
      <form phx-submit="v2_save_form_edit" class="space-y-3">
        <input type="hidden" name="form_id" value={@editing_form.id} />
        
        <div class="grid grid-cols-2 gap-3">
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Form Name</label>
            <input 
              type="text" 
              name="form[name]" 
              value={@editing_form.name}
              required 
              class="w-full border rounded px-2 py-1 text-xs" 
            />
          </div>
          
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Page</label>
            <select name="form[page_id]" required class="w-full border rounded px-2 py-1 text-xs bg-white">
              <%= for page <- @all_pages do %>
                <option value={page.id} selected={@editing_form.page_id == page.id}>
                  📄 <%= page.name %>
                </option>
              <% end %>
            </select>
          </div>
        </div>
        
        <div class="grid grid-cols-3 gap-3">
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Form Type</label>
            <select name="form[type]" class="w-full border rounded px-2 py-1 text-xs bg-white">
              <option value="form" selected={Map.get(@editing_form, :type) == "form" or Map.get(@editing_form, :type) == nil}>Form</option>
              <option value="confirmation" selected={Map.get(@editing_form, :type) == "confirmation"}>Confirmation</option>
              <option value="summary" selected={Map.get(@editing_form, :type) == "summary"}>Summary</option>
            </select>
          </div>
          
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Order</label>
            <input 
              type="number" 
              name="form[order]" 
              value={@editing_form.order}
              min="0"
              class="w-full border rounded px-2 py-1 text-xs" 
            />
          </div>
          
          <div>
            <label class="block text-xs font-medium text-gray-700 mb-1">Value</label>
            <input 
              type="text" 
              name="form[value]" 
              value={Map.get(@editing_form, :value, "")}
              placeholder="Optional value"
              class="w-full border rounded px-2 py-1 text-xs" 
            />
          </div>
        </div>
        
        <div>
          <label class="block text-xs font-medium text-gray-700 mb-1">Submit To URL</label>
          <input 
            type="text" 
            name="form[submit_to]" 
            value={@editing_form.submit_to}
            placeholder="https://example.com/submit"
            class="w-full border rounded px-2 py-1 text-xs" 
          />
        </div>
        
        <div class="flex items-center space-x-4">
          <label class="flex items-center text-xs">
            <input 
              type="checkbox" 
              name="form[active]" 
              checked={@editing_form.active}
              class="mr-2" 
            />
            Enabled
          </label>
          <label class="flex items-center text-xs">
            <input 
              type="checkbox" 
              name="form[app_view]" 
              checked={Map.get(@editing_form, :app_view, true)}
              class="mr-2" 
            />
            Show in App View
          </label>
        </div>
        
        <div class="flex items-center justify-end space-x-2 pt-3 border-t">
          <button 
            type="button"
            phx-click="v2_cancel_edit_form" 
            class="text-xs px-3 py-1 border border-gray-300 rounded text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button 
            type="submit" 
            class="text-xs px-3 py-1 bg-indigo-600 text-white rounded hover:bg-indigo-700"
          >
            Save Changes
          </button>
        </div>
      </form>
    <% else %>
      <div class="text-xs text-red-600">Form not found.</div>
    <% end %>
    """
  end
end
