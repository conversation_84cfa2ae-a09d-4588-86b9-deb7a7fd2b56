defmodule ServiceManagerWeb.Router do
  use ServiceManagerWeb, :router

  import ServiceManagerWeb.WalletUserAuth
  import ServiceManagerWeb.Plugs.SystemUserAuth
  import Phoenix.LiveDashboard.Router

  pipeline :browser do
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, html: {ServiceManagerWeb.Layouts, :root}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug :fetch_current_wallet_user
    plug :fetch_current_user
  end

  pipeline :backend_browser do
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, html: {ServiceManagerWeb.Layouts, :backend}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug :fetch_current_user
  end

  pipeline :auth_browser do
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, html: {ServiceManagerWeb.Layouts, :auth}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug :fetch_current_user
  end

  pipeline :info do
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, html: {ServiceManagerWeb.Layouts, :info}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug :fetch_current_wallet_user
    plug :fetch_current_user
  end

  pipeline :api do
    plug :accepts, ["json"]
    plug ServiceManagerWeb.Controllers.Api.Plugs.IpWhitelistPlug
  end

  pipeline :mobile_banking_api do
    plug :accepts, ["json"]
    plug ServiceManagerWeb.Controllers.Api.Plugs.IpWhitelistPlug
    plug ServiceManagerWeb.Plugs.CheckUserDisabled
  end

  pipeline :authenticated_api do
    plug :accepts, ["json"]
    plug ServiceManagerWeb.Controllers.Api.Plugs.IpWhitelistPlug
    plug ServiceManagerWeb.Plugs.AuthPlug
    plug ServiceManagerWeb.Plugs.CheckUserDisabled
    plug ServiceManagerWeb.Plugs.AccountStatusPlug
  end

  pipeline :authenticated_wallet_api do
    plug :accepts, ["json"]
    plug ServiceManagerWeb.Controllers.Api.Plugs.IpWhitelistPlug
    plug ServiceManagerWeb.Plugs.WalletAuthPlug
    plug ServiceManagerWeb.Controllers.Api.Plugs.SyncWalletWithAccount
    plug ServiceManagerWeb.Plugs.AccountStatusPlug
  end

  pipeline :authenticated_wallet_api_key do
    plug :accepts, ["json"]
    plug ServiceManagerWeb.Controllers.Api.Plugs.IpWhitelistPlug
    plug ServiceManagerWeb.Plugs.WalletAuthApiKeyPlug
    plug ServiceManagerWeb.Plugs.AccountStatusPlug
  end

  pipeline :third_party_api_key do
    plug :accepts, ["json"]
    plug ServiceManagerWeb.Controllers.Api.Plugs.IpWhitelistPlug
    plug ServiceManagerWeb.Plugs.ThirdPartyPlug
    plug ServiceManagerWeb.Plugs.AccountStatusPlug
  end

  scope "/", ServiceManagerWeb do
    pipe_through :backend_browser
  end

  # =================================================================================
  # THIRD PARTY API ROUTES
  # Access Type: Third party systems with API keys
  # Pipeline: :third_party_api_key (IP whitelist + third party auth + account status)
  # =================================================================================
  scope "/api/third-party", ServiceManagerWeb do
    pipe_through :third_party_api_key

    # Mobile Forms API
    scope "/mobile-forms" do
      # Mobile App Form Retrieval API
      post "/form", Api.MobileAppFormsController, :get_form

      # Field Management APIs
      post "/fields/list", Api.MobileAppFormsController, :list_fields
      post "/fields/get", Api.MobileAppFormsController, :get_field
      post "/fields/create", Api.MobileAppFormsController, :create_field
      post "/fields/update", Api.MobileAppFormsController, :update_field
      post "/fields/delete", Api.MobileAppFormsController, :delete_field

      # Hierarchy Management APIs
      post "/forms/create", Api.MobileAppFormsController, :create_form
      post "/forms/list", Api.MobileAppFormsController, :list_forms
      post "/screens/create", Api.MobileAppFormsController, :create_screen
      post "/screens/list", Api.MobileAppFormsController, :list_screens
      post "/pages/create", Api.MobileAppFormsController, :create_page
      post "/pages/list", Api.MobileAppFormsController, :list_pages
      # Mobile Forms API V2
      scope "/mobile-forms/v2" do
        # Discovery
        post "/screens/list", Api.MobileAppFormsV2Controller, :list_screens
        post "/pages/list", Api.MobileAppFormsV2Controller, :list_pages
        post "/forms/list", Api.MobileAppFormsV2Controller, :list_forms
        post "/structure", Api.MobileAppFormsV2Controller, :structure
        # Retrieval
        post "/form", Api.MobileAppFormsV2Controller, :get_form
        # CRUD
        post "/screens/create", Api.MobileAppFormsV2Controller, :create_screen
        post "/screens/update", Api.MobileAppFormsV2Controller, :update_screen
        post "/screens/delete", Api.MobileAppFormsV2Controller, :delete_screen

        post "/pages/create", Api.MobileAppFormsV2Controller, :create_page
        post "/pages/update", Api.MobileAppFormsV2Controller, :update_page
        post "/pages/delete", Api.MobileAppFormsV2Controller, :delete_page

        post "/forms/create", Api.MobileAppFormsV2Controller, :create_form
        post "/forms/update", Api.MobileAppFormsV2Controller, :update_form
        post "/forms/delete", Api.MobileAppFormsV2Controller, :delete_form

        post "/fields/create", Api.MobileAppFormsV2Controller, :create_field
        post "/fields/update", Api.MobileAppFormsV2Controller, :update_field
        post "/fields/delete", Api.MobileAppFormsV2Controller, :delete_field
      end

    end

    # Card Management
    scope "/cards" do
      post "/create", Api.CardsController, :create
      post "/validate", Api.CardsController, :validate
      post "/status", Api.CardsController, :status
      post "/link", Api.CardsController, :link_account
      post "/block", Api.CardsController, :block
      post "/activate", Api.CardsController, :activate
      post "/limits", Api.CardsController, :update_limits
      post "/change-status", Api.CardsController, :change_status
    end

    scope "/callback" do
      post "/register", CallbackRegistrationController, :register
      post "/list", CallbackRegistrationController, :list
      post "/show", CallbackRegistrationController, :show
      post "/update", CallbackRegistrationController, :update
      post "/delete", CallbackRegistrationController, :delete
      post "/status", CallbackRegistrationController, :status
      post "/report", CallbackRegistrationController, :report

      # Schema validation endpoints
      scope "/schema" do
        post "/fields", CallbackSchemaController, :list_fields
        post "/validate", CallbackSchemaController, :validate_schema
        post "/test", CallbackSchemaController, :test_mapping
      end
    end

    scope "/wallet" do
      post "/register", WalletAuthenticationController, :register
      post "/lookup", WalletController, :get_wallet_third_party
      post "/balance", WalletController, :get_balance
      post "/transactions", WalletController, :get_transactions
      post "/topup", WalletController, :topup
      post "/withdraw", WalletController, :withdraw
      post "/wallet_to_wallet_transfer", WalletController, :third_party_transfer_funds
      post "/wallet_to_bank_transfer", WalletController, :third_party_transfer_to_account

      post "/reverse_wallet_to_bank_transfer",
           WalletController,
           :reverse_third_party_transfer_to_account

      post "/update", WalletController, :update_wallet
      post "/close", WalletController, :close_wallet
      post "/update_password", WalletAuthenticationController, :update_password
      post "/set_memorable_word", WalletMemorableWordController, :set_wallet_memorable_word
      post "/otp/generate", OTPController, :generate_wallet_otp
      post "/otp/validate", OTPController, :validate_wallet_otp

      post "/freeze", WalletController, :freeze_wallet
      post "/unfreeze", WalletController, :unfreeze_wallet
      post "/block", WalletController, :block_wallet
      post "/unblock", WalletController, :unblock_wallet
      post "/lock", WalletController, :lock_wallet
      post "/unlock", WalletController, :unlock_wallet
    end

    scope "/mobile-banking" do
      post "/otp/generate", OTPController, :generate
      post "/otp/validate", OTPController, :validate
      post "/to_beneficiary", TransfersController, :create
      post "/bank_to_bank_transfer", TransfersController, :bank_to_bank
      post "/bank_to_other_bank_transfer", TransfersController, :bank_to_other_bank
      post "/bank_to_wallet_transfer", TransfersController, :account_to_wallet
      post "/reverse_bank_to_wallet_transfer", TransfersController, :reverse_account_to_wallet
      post "/to_own_account", TransfersController, :self_transfer
      post "/to_wallet", TransfersController, :account_to_wallet
      post "/report", TransfersController, :self_transfer
      post "/validate", TransfersController, :validate
      post "/mb_account_details", AccountsController, :get_account_by_account_number
      post "/cbs_account_details", ProfileController, :get_account_by_number_v2
      post "/services/profile", ThirdPartyController, :profile_services
    end

    scope "/reports/transactions" do
      post "/lookup/by_reference", TransactionsLookupController, :get_by_reference
      post "/lookup/by_references", TransactionsLookupController, :get_by_references
      post "/lookup/pair", TransactionsLookupController, :get_transaction_pair
      post "/lookup/reversal_chain", TransactionsLookupController, :get_reversal_chain
      post "/lookup/mini_statement", TransactionsLookupController, :get_mini_statement
    end
  end

  # =================================================================================
  # WALLET API ROUTES
  # Access Type: Wallet users and wallet API key integrations
  # Multiple pipelines: :api, :authenticated_wallet_api, :authenticated_wallet_api_key
  # =================================================================================

  # Wallet Authentication (Public)
  scope "/api/auth/wallet", ServiceManagerWeb do
    pipe_through :api

    post "/sign-in", WalletAuthenticationController, :sign_in
    post "/sign-out", WalletAuthenticationController, :sign_out
    post "/sign-up", WalletAuthenticationController, :register
    post "/refresh-token", WalletAuthenticationController, :refresh_token
    post "/forgot-password", WalletAuthenticationController, :forgot_password
    post "/verify_memorable_word", WalletMemorableWordController, :wallet_verify_memorable_word
    post "/remove", WalletController, :remove_wallet
    post "/wallet_remote_details", WalletAuthenticationController, :wallet_remote_details
  end

  # Wallet Integration API (API Key Authentication)
  scope "/api/integration/wallets", ServiceManagerWeb do
    pipe_through :authenticated_wallet_api_key

    post "/register", WalletController, :new_wallet
    post "/lookup", WalletController, :get_wallet
    post "/balance", WalletController, :get_balance
    post "/transactions", WalletController, :get_transactions
    post "/topup", WalletController, :topup
    post "/withdraw", WalletController, :withdraw
    post "/transfer", WalletController, :transfer_funds
    post "/transfer_to_account", WalletController, :transfer_to_account
    post "/update", WalletController, :update_wallet
    post "/update_nickname", WalletController, :update_nickname
    post "/update_id_image", WalletController, :update_id_image
    post "/close", WalletController, :close_wallet
    post "/update_password", WalletAuthenticationController, :update_password
    post "/set_memorable_word", WalletMemorableWordController, :set_wallet_memorable_word
    post "/otp/generate", OTPController, :generate_wallet_otp
    post "/otp/validate", OTPController, :validate_wallet_otp

    # Card Management
    scope "/cards" do
      post "/create", Api.CardsController, :create
      post "/validate", Api.CardsController, :validate
      post "/status", Api.CardsController, :status
      post "/link", Api.CardsController, :link_account
      post "/block", Api.CardsController, :block
      post "/activate", Api.CardsController, :activate
      post "/limits", Api.CardsController, :update_limits
      post "/change-status", Api.CardsController, :change_status
    end

    # Wallet status management
    # Wallet status management
    post "/freeze", WalletController, :freeze_wallet
    post "/unfreeze", WalletController, :unfreeze_wallet
    post "/block", WalletController, :block_wallet
    post "/unblock", WalletController, :unblock_wallet
    post "/lock", WalletController, :lock_wallet
    post "/unlock", WalletController, :unlock_wallet
    post "/hide", WalletController, :hide_wallet
    post "/unhide", WalletController, :unhide_wallet

    # Alert settings
    post "/alerts/status", WalletController, :update_alerts_status
    post "/alerts/large-transaction", WalletController, :update_large_transaction_alert
    post "/alerts/low-balance", WalletController, :update_low_balance_alert
    post "/alerts/suspicious-activity", WalletController, :update_suspicious_activity_alert
    post "/bank-details", WalletController, :update_bank_details

    scope "/notification-settings", Api do
      post "/show", WalletNotificationSettingsController, :show
      post "/update", WalletNotificationSettingsController, :update
      post "/send", WalletNotificationSettingsController, :send_notification
      post "/limits", WalletNotificationSettingsController, :show
    end
  end

  # Wallet Transaction Reports (No explicit auth - handled by controller)
  scope "/api/transactions/wallet", ServiceManagerWeb do
    # Wallet transactions reports
    post "/report/get-by-mobile", TransactionsController, :get_wallet_transactions_by_mobile
    post "/report/by-status", TransactionsController, :wallet_transactions_by_status
    post "/report/by-type", TransactionsController, :wallet_transactions_by_type
    post "/report/by-date", TransactionsController, :wallet_transactions_by_date_range
    post "/report/search", TransactionsController, :search_wallet_transactions
  end

  # Wallet Beneficiaries (Wallet User Authentication)
  scope "/api/wallet/beneficiaries", ServiceManagerWeb do
    pipe_through :authenticated_wallet_api

    post "/", BeneficiariesController, :create
    post "/get-beneficiary", BeneficiariesController, :get
    post "/list", BeneficiariesController, :list
    post "/update", BeneficiariesController, :update
    post "/remove", BeneficiariesController, :remove
    post "/validate", BeneficiariesController, :validate
    post "/status", BeneficiariesController, :status
    post "/set_default", BeneficiariesController, :set_default
    post "/search", BeneficiariesController, :search
  end

  # Main Wallet API (Wallet User Authentication)
  scope "/api/wallets", ServiceManagerWeb do
    pipe_through :authenticated_wallet_api

    post "/register", WalletController, :new_wallet
    post "/lookup", WalletController, :get_wallet
    post "/balance", WalletController, :get_balance
    post "/transactions", WalletController, :get_transactions
    post "/topup", WalletController, :topup
    post "/withdraw", WalletController, :withdraw
    post "/transfer", WalletController, :transfer_funds
    post "/transfer_to_account", WalletController, :transfer_to_account
    post "/update", WalletController, :update_wallet
    post "/update_nickname", WalletController, :update_nickname
    post "/update_id_image", WalletController, :update_id_image
    post "/close", WalletController, :close_wallet
    post "/update_password", WalletAuthenticationController, :update_password
    post "/set_memorable_word", WalletMemorableWordController, :set_wallet_memorable_word
    post "/otp/generate", OTPController, :generate_wallet_otp
    post "/otp/validate", OTPController, :validate_wallet_otp

    scope "/device" do
      post "/", ActiveDevicesController, :wallet_device
      post "/list", ActiveDevicesController, :list_wallet_devices
      post "/enable", ActiveDevicesController, :enable_wallet_device
      post "/disable", ActiveDevicesController, :disable_wallet_device
      post "/discard", ActiveDevicesController, :discard_wallet_device
    end

    scope "/notification-settings", Api do
      post "/show", WalletNotificationSettingsController, :show
      post "/update", WalletNotificationSettingsController, :update
      post "/send", WalletNotificationSettingsController, :send_notification
      post "/limits", WalletNotificationSettingsController, :show
    end

    # Billers API for Wallets
    scope "/billers", Api do
      post "/types", BillersController, :get_biller_types
      post "/account-details", BillersController, :account_details
      post "/payment", BillersController, :process_payment
      post "/invoice", BillersController, :get_invoice
      post "/invoice-confirm", BillersController, :confirm_invoice
      post "/bundle-details", BillersController, :bundle_details
      post "/bundle-confirm", BillersController, :confirm_bundle
      post "/validate", BillersController, :validate_account
      post "/transaction", BillersController, :get_transaction
      post "/transaction-by-reference", BillersController, :get_transaction_by_reference
      post "/transactions", BillersController, :list_transactions
      post "/transaction-retry", BillersController, :retry_transaction
    end

    # Card Management
    scope "/cards" do
      post "/create", Api.CardsController, :create
      post "/validate", Api.CardsController, :validate
      post "/status", Api.CardsController, :status
      post "/link", Api.CardsController, :link_account
      post "/block", Api.CardsController, :block
      post "/activate", Api.CardsController, :activate
      post "/limits", Api.CardsController, :update_limits
      post "/change-status", Api.CardsController, :change_status
    end

    scope "/withdraws" do
      post "/cardless/wallet", Api.WithdrawsController, :cardless_wallet_withdraw
      post "/cardless/wallet/verify", Api.WithdrawsController, :verify_wallet_withdraw
    end

    # Wallet status management
    post "/freeze", WalletController, :freeze_wallet
    post "/unfreeze", WalletController, :unfreeze_wallet
    post "/block", WalletController, :block_wallet
    post "/unblock", WalletController, :unblock_wallet
    post "/lock", WalletController, :lock_wallet
    post "/unlock", WalletController, :unlock_wallet
    post "/hide", WalletController, :hide_wallet
    post "/unhide", WalletController, :unhide_wallet

    # Alert settings
    post "/alerts/status", WalletController, :update_alerts_status
    post "/alerts/large-transaction", WalletController, :update_large_transaction_alert
    post "/alerts/low-balance", WalletController, :update_low_balance_alert
    post "/alerts/suspicious-activity", WalletController, :update_suspicious_activity_alert
    post "/bank-details", WalletController, :update_bank_details
  end

  # =================================================================================
  # PUBLIC API ROUTES
  # Access Type: Public access (with IP whitelist)
  # Pipeline: :api (IP whitelist only)
  # =================================================================================

  # General OTP Service
  scope "/api/general/otp", ServiceManagerWeb do
    pipe_through :api

    post "/generate", OTPController, :generate_general_otp
    post "/validate", OTPController, :validate_general_otp
  end

  # Dynamic Route Handler
  scope "/", ServiceManagerWeb do
    pipe_through :authenticated_api

    # Forward all requests to /dynamic/* to the DynamicRouteController
    match :*, "api/dynamic/*path", DynamicRouteController, :handle
  end

  # Authentication API
  scope "/api/auth", ServiceManagerWeb do
    pipe_through :api

    post "/sign-in", AuthenticationController, :sign_in
    post "/sign-out", AuthenticationController, :sign_out
    post "/sign_up", AuthenticationController, :sign_up
    post "/refresh", AuthenticationController, :refresh
    post "/verification-status", AuthenticationController, :check_device_status
    post "/forgot_password", AuthenticationController, :forgot_password
    post "/register/register-merchant", MerchantsController, :register

    scope "/v1/aggregate" do
      get "/forms", DynamicRouteController, :forms
      get "/routes", DynamicRouteController, :dynamic_routes
    end

  end

  # Profile Management (Public)
  scope "/api/profile", ServiceManagerWeb do
    pipe_through :api
    post "/register", ProfileController, :register
    post "/remote_details", ProfileController, :get_account_by_number
    post "/link_account", ProfileController, :link_account
    post "/approve_profile", ProfileController, :approve
    post "/verify_memorable_word", MemorableWordController, :verify
  end

  scope "/api/mobile-forms/v2", ServiceManagerWeb do
    pipe_through :api

    post "/structure", Api.MobileAppFormsV2Controller, :structure

  end

  # General API endpoints (Public)
  scope "/api", ServiceManagerWeb do
    pipe_through :api
    post "/memorable_word/verify", MemorableWordController, :verify

    # Billers API - Public endpoints
    scope "/billers", Api do
      post "/types", BillersController, :get_biller_types
    end
  end

  # =================================================================================
  # AUTHENTICATED USER API ROUTES
  # Access Type: Mobile banking users with valid JWT tokens
  # Pipeline: :authenticated_api (IP whitelist + auth + user status + account status)
  # =================================================================================
  scope "/api", ServiceManagerWeb do
    pipe_through :authenticated_api

    # Triggers API - Dynamic function execution endpoints
    scope "/triggers" do
      # List all available triggers
      get "/", Api.TriggerController, :list_triggers

      # Get information about a specific trigger
      get "/info/*mount_path", Api.TriggerController, :get_trigger_info

      # Get rate limit information for a trigger
      get "/rate-limit/*mount_path", Api.TriggerController, :get_rate_limit_info

      # Execute a trigger via POST
      post "/*mount_path", Api.TriggerController, :execute

      # Test a trigger (admin/development only)
      post "/test/*mount_path", Api.TriggerController, :test_trigger
    end

    scope "/device" do
      post "/", ActiveDevicesController, :device
      post "/list", ActiveDevicesController, :list_devices
      post "/enable", ActiveDevicesController, :enable_device
      post "/disable", ActiveDevicesController, :disable_device
      post "/discard", ActiveDevicesController, :discard_device
    end

    scope "/activeDevices" do
      get "/Query", ActiveDevicesController, :index
      post "/Query", ActiveDevicesController, :index
      post "/RunCommand", ActiveDevicesController, :run_command
      get "/:id/Query", ActiveDevicesController, :get_data_by_id
    end

    scope "/otp" do
      post "/generate", OTPController, :generate
      post "/validate", OTPController, :validate
    end

    scope "/profile" do
      post "/", ProfileController, :update
      post "/details", ProfileController, :profile
      post "/update_password", ProfileController, :update_password
      post "/set_nickname", ProfileController, :update_nickname
      post "/allow_multi_session", ProfileController, :update_multi_session_setting
      post "/disable", ProfileController, :disable_user
      post "/enable", ProfileController, :enable_user
    end

    scope "/notification-settings", Api do
      post "/show", NotificationSettingsController, :show
      post "/update", NotificationSettingsController, :update
      post "/send", NotificationSettingsController, :send_notification
    end

    scope "/accounts" do
      post "/", AccountsController, :create
      post "/list", AccountsController, :list_accounts
      post "/account", AccountsController, :get_account_by_account_number
      post "/freeze_account", AccountsController, :freeze_account
      post "/unfreeze_account", AccountsController, :unfreeze_account
      post "/hide_account", AccountsController, :hide_account
      post "/unhide_account", AccountsController, :unhide_account
      post "/set_account_nickname", AccountsController, :set_account_nickname
    end

    scope "/wallet" do
      post "/", WalletController, :new_wallet
      post "/lookup", WalletController, :lookup_wallet
    end

    # Card Management
    scope "/cards" do
      post "/create", Api.CardsController, :create
      post "/validate", Api.CardsController, :validate
      post "/status", Api.CardsController, :status
      post "/link", Api.CardsController, :link_account
      post "/block", Api.CardsController, :block
      post "/activate", Api.CardsController, :activate
      post "/limits", Api.CardsController, :update_limits
      post "/change-status", Api.CardsController, :change_status
    end

    scope "/merchant" do
      post "/pay", MerchantsController, :pay
    end

    scope "/biometrics" do
      post "/enable", BiometricsController, :enable
      post "/disable", BiometricsController, :disable
    end

    scope "/memorable_word" do
      post "/set", MemorableWordController, :set
    end

    scope "/beneficiaries" do
      post "/", BeneficiariesController, :create
      post "/get-beneficiary", BeneficiariesController, :get
      post "/list", BeneficiariesController, :list
      post "/update", BeneficiariesController, :update
      post "/remove", BeneficiariesController, :remove
      post "/validate", BeneficiariesController, :validate
      post "/status", BeneficiariesController, :status
      post "/set_default", BeneficiariesController, :set_default
      post "/search", BeneficiariesController, :search
    end

    scope "/bills" do
      post "/verify", BillsController, :verify
      post "/pay", BillsController, :pay
    end

    # Billers API - Authenticated endpoints
    scope "/billers", Api do
      post "/account-details", BillersController, :account_details
      post "/payment", BillersController, :process_payment
      post "/invoice", BillersController, :get_invoice
      post "/invoice-confirm", BillersController, :confirm_invoice
      post "/bundle-details", BillersController, :bundle_details
      post "/bundle-confirm", BillersController, :confirm_bundle
      post "/validate", BillersController, :validate_account
      post "/transaction", BillersController, :get_transaction
      post "/transaction-by-reference", BillersController, :get_transaction_by_reference
      post "/transactions", BillersController, :list_transactions
      post "/transaction-retry", BillersController, :retry_transaction
    end

    scope "/transfers" do
      post "/to_beneficiary", TransfersController, :create
      post "/transfer", TransfersController, :create
      post "/to_own_account", TransfersController, :self_transfer
      post "/to_wallet", TransfersController, :account_to_wallet
      post "/bank_to_bank_transfer", TransfersController, :bank_to_bank
      post "/bank_to_other_bank_transfer", TransfersController, :bank_to_other_bank
      post "/report", TransfersController, :self_transfer
      post "/validate", TransfersController, :validate

      # Fund Requests
      post "/fund_requests/create", API.FundRequestsController, :create
      post "/fund_requests/list", API.FundRequestsController, :index
      post "/fund_requests/pending", API.FundRequestsController, :pending_requests
      post "/fund_requests/approve", API.FundRequestsController, :approve
      post "/fund_requests/reject", API.FundRequestsController, :reject
    end

    scope "/withdraws" do
      post "/cardless/initiate", Api.WithdrawsController, :cardless_withdraw
      post "/cardless/verify", Api.WithdrawsController, :verify_withdraw
    end

    scope "/transactions" do
      post "/reportPerAccount", TransactionsController, :report

      # Regular transactions reports
      post "/report/by-account", TransactionsController, :transactions_by_account
      post "/report/by-status", TransactionsController, :transactions_by_status
      post "/report/by-type", TransactionsController, :transactions_by_type
      post "/report/by-date", TransactionsController, :transactions_by_date_range
      post "/report/search", TransactionsController, :search_transactions

      post "/report/mini-statement",
           TransactionsLookupController,
           :get_mini_statement_mobile_banking
    end

    scope "/chequebook/request" do
      post "/create", ChequeBookController, :create
      post "/list", ChequeBookController, :list
      post "/detail", ChequeBookController, :detail
      post "/update", ChequeBookController, :update
      post "/delete", ChequeBookController, :delete
    end

    scope "/cheque" do
      post "/action", Api.ChequeRequestController, :create
    end

    # =========== Loan Routes =============
    scope "/loans" do
      post "/report", LoansController, :report
      post "/loan_details", LoansController, :loan_details
      post "/products", LoansController, :products
      post "/apply", LoansController, :apply
      # Mobile Forms API V2


      post "/confirm", LoansController, :confirm_loan
      get "/:id", LoansController, :show
      post "/eligible", LoansController, :eligible

      post "/repay", LoansController, :repay
      post "/pending_loans", LoansController, :pending_loans
    end

    # Mobile Forms API
    scope "/mobile-forms" do
      # Mobile App Form Retrieval API
      post "/form", Api.MobileAppFormsController, :get_form

      # Field Management APIs
      post "/fields/list", Api.MobileAppFormsController, :list_fields
      post "/fields/get", Api.MobileAppFormsController, :get_field
      post "/fields/create", Api.MobileAppFormsController, :create_field
      post "/fields/update", Api.MobileAppFormsController, :update_field
      post "/fields/delete", Api.MobileAppFormsController, :delete_field

      # Hierarchy Management APIs
      post "/forms/create", Api.MobileAppFormsController, :create_form
      post "/forms/list", Api.MobileAppFormsController, :list_forms
      post "/screens/create", Api.MobileAppFormsController, :create_screen
      post "/screens/list", Api.MobileAppFormsController, :list_screens
      post "/pages/create", Api.MobileAppFormsController, :create_page
      post "/pages/list", Api.MobileAppFormsController, :list_pages

      scope "/v2" do
        # Discovery
        post "/screens/list", Api.MobileAppFormsV2Controller, :list_screens
        post "/pages/list", Api.MobileAppFormsV2Controller, :list_pages
        post "/forms/list", Api.MobileAppFormsV2Controller, :list_forms
        post "/structure", Api.MobileAppFormsV2Controller, :structure
        # Retrieval
        post "/form", Api.MobileAppFormsV2Controller, :get_form
        # CRUD
        post "/screens/create", Api.MobileAppFormsV2Controller, :create_screen
        post "/screens/update", Api.MobileAppFormsV2Controller, :update_screen
        post "/screens/delete", Api.MobileAppFormsV2Controller, :delete_screen

        post "/pages/create", Api.MobileAppFormsV2Controller, :create_page
        post "/pages/update", Api.MobileAppFormsV2Controller, :update_page
        post "/pages/delete", Api.MobileAppFormsV2Controller, :delete_page

        post "/forms/create", Api.MobileAppFormsV2Controller, :create_form
        post "/forms/update", Api.MobileAppFormsV2Controller, :update_form
        post "/forms/delete", Api.MobileAppFormsV2Controller, :delete_form

        post "/fields/create", Api.MobileAppFormsV2Controller, :create_field
        post "/fields/update", Api.MobileAppFormsV2Controller, :update_field
        post "/fields/delete", Api.MobileAppFormsV2Controller, :delete_field
      end
    end
  end




  # =================================================================================
  # BACKEND/ADMIN WEB INTERFACE ROUTES
  # Access Type: System admin users via web browser
  # Pipeline: :backend_browser, :auth_browser, :info, :browser (various browser auth)
  # =================================================================================

  # Development Tools (if enabled)
  # if Application.compile_env(:service_manager, :dev_routes) do
  #   import Phoenix.LiveDashboard.Router
  #   scope "/dev" do
  #     pipe_through :browser
  #     live_dashboard "/dashboard", metrics: ServiceManagerWeb.Telemetry
  #     forward "/mailbox", Plug.Swoosh.MailboxPreview
  #   end
  # end

  # System Monitoring
  scope "/monitor" do
    pipe_through :browser

    live_dashboard "/dashboard", metrics: ServiceManagerWeb.Telemetry
    live "/logs", ServiceManagerWeb.LogsLive, :index
  end

  # Main Admin Backend
  scope "/mobileBanking", ServiceManagerWeb.Backend do
    pipe_through [:backend_browser, :require_authenticated_user]

    live_session :require_authenticated_user,
      on_mount: [{ServiceManagerWeb.Plugs.SystemUserAuth, :ensure_authenticated}] do
      live "/", HomeLive.Index, :index
      live "/online-users", OnlineUsersLive, :index
      live "/online-users/filter", OnlineUsersLive, :filter
      live "/online-users/export", OnlineUsersLive, :export
      # Add this line for the new dashboard route
      live "/dashboard", DashboardLive, :index
      live "/monitor/logs", LogsLive, :index
      live "/profile", AdminProfileLive, :show
      live "/profile/change-password", ChangePasswordLive, :index
      live "/switchboard", SwitchboardLive.Index, :index

      scope "/monitor/backend/logs", LogsLive do
        live "/", Index, :index
        live "/filter", Index, :filter
        live "/ExcelExportFilter", Index, :excel_export
        live "/:id/show", Index, :show
      end

      scope "/transactions", TransactionsLive do
        live "/", Index, :index
        live "/filter", Index, :filter
        live "/ExcelExportFilter", Index, :excel_export
        live "/new", Index, :new
        live "/:id/edit", Index, :edit
        live "/:id/show", Index, :show
        live "/:id", Show, :show
        live "/:id/show/edit", Show, :edit
        live "/:id/reverse", Index, :reverse
      end

      scope "/fund-requests", FundRequestsLive do
        live "/", Index, :index
        live "/filter", Index, :filter
        live "/ExcelExportFilter", Index, :excel_export
        live "/:id/show", Index, :show
      end

      scope "/Customers", CustomersLiveLive do
        live "/", Index, :index
        live "/filter", Index, :filter
        live "/ExcelExportFilter", Index, :excel_export
        live "/new", Index, :new
        live "/:id/edit", Index, :edit
        live "/:id", Show, :show
        live "/:id/show/edit", Show, :edit
      end

      scope "/WalletTransactions", WalletTransactionsLive do
        live "/", Index, :index
        live "/filter", Index, :filter
        live "/ExcelExportFilter", Index, :excel_export
        live "/new", Index, :new
        live "/:id/edit", Index, :edit
        live "/:id", Show, :show
        live "/:id/show/edit", Show, :edit
        live "/:id/reverse", Index, :reverse
      end

      scope "/systemSettings", SettingsLive do
        live "/", Index, :index
        live "/t24Configs", Index, :t24
        live "/generalSettings", Index, :general_settings
        live "/notifications", Index, :notifications
        live "/images", Index, :images

        scope "/More" do
          live "/", Index, :more
          live "/new", Index, :more_new
          live "/:id/edit", Index, :more_edit
        end
      end

      scope "/ip-whitelist" do
        live "/", IpWhitelistLive, :index
        live "/new", IpWhitelistLive, :new
        live "/:id/edit", IpWhitelistLive, :edit
      end

      scope "/SystemLogs", LogsLive do
        live "/t24", Index, :index
        live "/oban", Index, :oban_jobs
        live "/t24/:id", Show, :t24
        live "/oban/:id", Show, :oban_jobs
      end

      scope "/sms_logs", SmsLogsLive do
        live "/", Index, :index
        live "/:id/details", Index, :show
      end

      scope "/callbacks" do
        live "/", CallbackLive.Index, :index
        live "/:id", CallbackLive.Index, :show
      end

      scope "/WalletsOverview", WalletsLive do
        live "/", Index, :index
        live "/new", Index, :new
        live "/:id/edit", Index, :edit
        live "/:id", Index, :show
        live "/:id/upgrade", Index, :upgrade
      end

      # Wallet Tiers Management
      scope "/WalletTiers", WalletTiersLive do
        live "/", Index, :index
        live "/new", Index, :new
        live "/:id/edit", Index, :edit
      end

      live "/ruserRoles&permissions", RolesAndPermissionLive.Index, :index
      live "/ruserRoles&permissions/new", RolesAndPermissionLive.Index, :new
      live "/ruserRoles&permissions/:id/edit", RolesAndPermissionLive.Index, :edit
      live "/ruserRoles&permissions/:id", RolesAndPermissionLive.Show, :show
      live "/ruserRoles&permissions/:id/show/edit", RolesAndPermissionLive.Show, :edit

      live "/beficiaries", BeficiariesLive.Index, :index
      live "/beficiaries/new", BeficiariesLive.Index, :new
      live "/beficiaries/openAccount", BeficiariesLive.Index, :open_account
      live "/beficiaries/:id/edit", BeficiariesLive.Index, :edit
      live "/beficiaries/:id", BeficiariesLive.Show, :show
      live "/beficiaries/:id/show/edit", BeficiariesLive.Show, :edit

      scope "/user_managements", UserManagementLive do
        live "/", Index, :index
        live "/new", Index, :new
        live "/openAccount", Index, :open_account
        live "/:id/edit", Index, :edit
        live "/:id", Index, :show
        live "/:id/show/edit", Show, :edit
      end

      scope "/SystemUsers", SystemUserManagementLive do
        live "/", Index, :index
        live "/new", Index, :new
        live "/openAccount", Index, :open_account
        live "/:id/edit", Index, :edit
        live "/:id", Show, :show
        live "/:id/show/edit", Show, :edit
      end

      scope "/dynamic-forms", DynamicFormsLive do
        # Dashboard
        live "/", Dashboard, :dashboard

        # Forms
        live "/forms", FormsIndex, :index
        live "/forms/new", FormsIndex, :new
        live "/forms/:id/edit", FormsIndex, :edit
        live "/forms/wizard/:id/edit", FormsIndex, :edit_wizard

        # Processes & Plugins
        live "/processes", ProcessesIndex, :index
        live "/processes/new", ProcessesIndex, :new
        live "/processes/:id/edit", ProcessEditLive, :edit
        live "/processes/:id/chain", ProcessChainLive, :chain

        # Code Steps Library
        live "/code-steps", CodeStepsIndex, :index
        live "/code-steps/new", CodeStepsIndex, :new
        live "/code-steps/:id/edit", CodeStepsIndex, :edit

        # Routes
        live "/routes", RoutesIndex, :index
        live "/routes/new", RoutesIndex, :new
        live "/routes/:id/edit", RoutesIndex, :edit
        live "/routes/:id/link-forms", RoutesIndex, :link_forms
        live "/routes/:id/details", RouteDetailsLive, :show

        # Triggers
        live "/triggers", TriggersIndex, :index
        live "/triggers/new", TriggersIndex, :new
        live "/triggers/:id/edit", TriggersIndex, :edit
        live "/triggers/monitoring", TriggerMonitoringLive, :index

        # Integrations
        live "/integrations", IntegrationsIndex, :index
        live "/integrations/new", IntegrationsIndex, :new
        live "/integrations/:id/edit", IntegrationsIndex, :edit

        # Request Templates
        live "/integrations/templates/new", IntegrationsIndex, :new_template
        live "/integrations/templates/:id/edit", IntegrationsIndex, :edit_template
        live "/integrations/templates/:id/preview", IntegrationsIndex, :preview_template

        # Legacy routes for backwards compatibility
        live "/link", Index, :link_form
        live "/processes/link", Index, :link_process
      end

      # Mobile Forms Management
      scope "/mobile-forms", MobileFormsLive do
        live "/", Index, :index
        live "/new", Index, :new
        live "/:id/edit", Index, :edit
      end

      scope "/cheque-book-requests", ChequeBookRequestLive do
        live "/", Index, :index
        live "/new", Index, :new
        live "/filter", Index, :filter
        live "/export", Index, :export
        live "/:id/edit", Index, :edit
        live "/:id", Index, :show
      end

      scope "/cheque-requests", ChequeRequestLive do
        live "/", Index, :index
        live "/new", Index, :new
        live "/filter", Index, :filter
        live "/export", Index, :export
        live "/:id/edit", Index, :edit
        live "/:id", Index, :show
      end

      scope "/fund-requests", FundRequestsLive do
        live "/", Index, :index
        live "/filter", Index, :filter
        live "/ExcelExportFilter", Index, :excel_export
        live "/:id", Index, :show
      end

      scope "/cardless-withdraws", CardlessWithdrawsLive do
        live "/", Index, :index
        live "/filter", Index, :filter
        live "/ExcelExportFilter", Index, :excel_export
        live "/:id/show", Index, :show
      end

      scope "/versions", VersionLive do
        live "/", Index, :index
        live "/:id", Index, :show
      end

      scope "/cards", CardManagementLive do
        live "/", Index, :index
        live "/new", Index, :new
        live "/:id/edit", Index, :edit
        live "/:id", Index, :show
      end

      live "/currencies", CurrencyLive.Index, :index
      live "/currencies/new", CurrencyLive.Index, :new
      live "/currencies/:id/edit", CurrencyLive.Index, :edit
      live "/currencies/:id", CurrencyLive.Show, :show
      live "/currencies/:id/show/edit", CurrencyLive.Show, :edit

      live "/exchange_rates", ExchangeRateLive.Index, :index
      live "/exchange_rates/new", ExchangeRateLive.Index, :new
      live "/exchange_rates/:id/edit", ExchangeRateLive.Index, :edit
      live "/exchange_rates/:id", ExchangeRateLive.Show, :show
      live "/exchange_rates/:id/show/edit", ExchangeRateLive.Show, :edit

      live "/fees&Chargers", FeeLive.Index, :index
      live "/fees&Chargers/new", FeeLive.Index, :new
      live "/fees&Chargers/:id/edit", FeeLive.Index, :edit
      live "/fees&Chargers/:id", FeeLive.Show, :show
      live "/fees&Chargers/:id/show/edit", FeeLive.Show, :edit

      live "/VirtualCardsAPIConfigs", VirtualCardsApiConfigLive.Index, :index
      live "/VirtualCardsAPIConfigs/new", VirtualCardsApiConfigLive.Index, :new
      live "/VirtualCardsAPIConfigs/:id/edit", VirtualCardsApiConfigLive.Index, :edit
      live "/VirtualCardsAPIConfigs/:id", VirtualCardsApiConfigLive.Show, :show
      live "/VirtualCardsAPIConfigs/:id/show/edit", VirtualCardsApiConfigLive.Show, :edit

      live "/paymentMethods", PaymentMethodLive.Index, :index
      live "/paymentMethods/new", PaymentMethodLive.Index, :new
      live "/paymentMethods/:id/edit", PaymentMethodLive.Index, :edit
      live "/paymentMethods/:id", PaymentMethodLive.Show, :show
      live "/paymentMethods/:id/show/edit", PaymentMethodLive.Show, :edit

      scope "/loan_mgt", LoanMgt do
        scope "/partnerships", LoanPartnershipsLive do
          live "/", Index, :index
          live "/new", Index, :new
          live "/:id/edit", Index, :edit
          live "/filter", Index, :filter
          live "/ExcelExportFilter", Index, :excel_export
        end

        scope "/customers", LoanCustomersLive do
          live "/", Index, :index
          live "/new", Index, :new
          live "/:id/edit", Index, :edit
          live "/filter", Index, :filter
          live "/ExcelExportFilter", Index, :excel_export
          live "/upload_customers", Upload, :index
          live "/:batch_ref/batch_customers", BatchCustomers, :index
        end

        scope "/customer_batches", LoanCustomersLive do
          live "/", Batch, :index
          live "/filter", Batch, :filter
          live "/ExcelExportFilter", Batch, :excel_export
        end

        scope "/products", LoanProductsLive do
          live "/", Index, :index
          live "/new", Index, :new
          live "/:id/edit", Index, :edit
          live "/filter", Index, :filter
          live "/ExcelExportFilter", Index, :excel_export
        end

        live "/upload_products", LoanProductsLive.Upload, :index

        scope "/loan_reports", LoanReportsLive do
          live "/", Index, :index
          live "/filter", Index, :filter
          live "/ExcelExportFilter", Index, :excel_export
        end

        scope "/loan_transactions", LoanTransactionsLive do
          live "/", Index, :index
          live "/filter", Index, :filter
          live "/ExcelExportFilter", Index, :excel_export
        end

        scope "/charges", ChargesLive do
          live "/", Index, :index
          live "/filter", Index, :filter
          live "/ExcelExportFilter", Index, :excel_export
        end

        get "/export/template", ExportController, :download_template
      end
    end

    post "/users/log_in", UserSessionController, :create
  end

  # Backend Reporting
  scope "/mobileBankingReporting", ServiceManagerWeb.Reports do
    pipe_through [:backend_browser, :require_authenticated_user]

    post "/Transactions/ExcelExport", TransactionsExportReportsController, :excel
    post "/WalletTransactions/ExcelExport", TransactionsExportReportsController, :wallet_excel
    post "/Customers/ExcelExport", CustomerReportsController, :excel
  end

  # System User Authentication Pages
  scope "/", ServiceManagerWeb do
    pipe_through [:auth_browser, :redirect_if_user_is_authenticated]

    live_session :redirect_if_user_is_authenticated,
      on_mount: [{ServiceManagerWeb.Plugs.SystemUserAuth, :redirect_if_user_is_authenticated}] do
      live "/users/register", UserRegistrationLive, :new
      live "/", UserLoginLive, :new
      live "/users/log_in", UserLoginLive, :new
      live "/users/reset_password", UserForgotPasswordLive, :new
      live "/users/reset_password/:token", UserResetPasswordLive, :edit
    end

    post "/users/log_in", UserSessionController, :create
  end

  # Public Info Pages
  scope "/", ServiceManagerWeb do
    pipe_through [:info]
    live "/about-us", AboutUsLive, :new
  end

  # System User Settings (Authenticated)
  scope "/", ServiceManagerWeb do
    pipe_through [:browser, :require_authenticated_user]

    live_session :ensure_authenticated,
      on_mount: [{ServiceManagerWeb.Plugs.SystemUserAuth, :ensure_authenticated}] do
      live "/users/settings", UserSettingsLive, :edit
      live "/users/settings/confirm_email/:token", UserSettingsLive, :confirm_email
    end
  end

  # System User Session Management
  scope "/", ServiceManagerWeb do
    pipe_through [:browser]

    delete "/users/log_out", UserSessionController, :delete
    get "/users/log_out", UserSessionController, :delete

    live_session :current_user,
      on_mount: [{ServiceManagerWeb.UserAuth, :mount_current_user}] do
      live "/users/confirm/:token", UserConfirmationLive, :edit
      live "/users/confirm", UserConfirmationInstructionsLive, :new
    end
  end

  # =================================================================================
  # WALLET USER WEB INTERFACE ROUTES
  # Access Type: Wallet users via web browser
  # Pipeline: :browser with wallet authentication
  # =================================================================================

  # Wallet User Authentication Pages
  scope "/", ServiceManagerWeb do
    pipe_through [:browser, :redirect_if_wallet_user_is_authenticated]

    live_session :redirect_if_wallet_user_is_authenticated,
      on_mount: [{ServiceManagerWeb.WalletUserAuth, :redirect_if_wallet_user_is_authenticated}] do
      live "/walletusers/register", WalletUserRegistrationLive, :new
      live "/walletusers/log_in", WalletUserLoginLive, :new
      live "/walletusers/reset_password", WalletUserForgotPasswordLive, :new
      live "/walletusers/reset_password/:token", WalletUserResetPasswordLive, :edit
    end

    post "/walletusers/log_in", WalletUserSessionController, :create
  end

  # Wallet User Settings (Authenticated)
  scope "/", ServiceManagerWeb do
    pipe_through [:browser, :require_authenticated_wallet_user]

    live_session :require_authenticated_wallet_user,
      on_mount: [{ServiceManagerWeb.WalletUserAuth, :ensure_authenticated}] do
      live "/walletusers/settings", WalletUserSettingsLive, :edit
      live "/walletusers/settings/confirm_email/:token", WalletUserSettingsLive, :confirm_email
    end
  end

  # Wallet User Session Management
  scope "/", ServiceManagerWeb do
    pipe_through [:browser]

    delete "/walletusers/log_out", WalletUserSessionController, :delete

    live_session :current_wallet_user,
      on_mount: [{ServiceManagerWeb.WalletUserAuth, :mount_current_wallet_user}] do
      live "/walletusers/confirm/:token", WalletUserConfirmationLive, :edit
      live "/walletusers/confirm", WalletUserConfirmationInstructionsLive, :new
    end
  end
end
