defmodule ServiceManagerWeb.Api.Services.Local.MobileFormsV2Service do
  @moduledoc """
  V2 Mobile Forms service implementing the hierarchy:
  Screens -> Pages -> Forms -> Fields

  Provides discovery, retrieval, and CRUD helpers.
  """
  import Ecto.Query
  import Logger
  alias ServiceManager.Repo

  alias ServiceManagerWeb.Api.{
    ScreenSchema,
    PageSchema,
    FormV2Schema,
    FormFieldV2Schema,
  }

  # =====================
  # Discovery / Retrieval
  # =====================

  def list_screens(params \\ %{}) do
    base = from s in ScreenSchema
    query =
      base
      |> maybe_where(:version, params)
      |> maybe_where_active(params)
      |> order_by([s], asc: s.order)

    {:ok, Repo.all(query)}
  end

  def list_pages(params) do
    cond do
      # Handle explicit screen_id (including nil for "all pages")
      Map.has_key?(params, :screen_id) or Map.has_key?(params, "screen_id") ->
        screen_id = get_in(params, ["screen_id"]) || params[:screen_id]

        query = if screen_id do
          from p in PageSchema,
            where: p.screen_id == ^screen_id,
            order_by: [asc: p.order]
        else
          # When screen_id is nil, return all pages
          from p in PageSchema,
            order_by: [asc: p.order]
        end

        {:ok, Repo.all(query)}

      screen = get_in(params, ["screen"]) || params[:screen] ->
        version = get_in(params, ["version"]) || params[:version]
        with {:ok, screen} <- find_screen(screen, version) do
          list_pages(%{screen_id: screen.id})
        end

      true ->
        {:error, "screen_id or (screen, version) required"}
    end
  end

  def list_forms(params) do
    cond do
      # Handle explicit page_id (including nil for "all forms")
      Map.has_key?(params, :page_id) or Map.has_key?(params, "page_id") ->
        page_id = get_in(params, ["page_id"]) || params[:page_id]

        query = if page_id do
          from f in FormV2Schema,
            where: f.page_id == ^page_id,
            order_by: [asc: f.order]
        else
          # When page_id is nil, return all forms
          from f in FormV2Schema,
            order_by: [asc: f.order]
        end

        {:ok, Repo.all(query)}

      page_name = get_in(params, ["page"]) || params[:page] ->
        screen = get_in(params, ["screen"]) || params[:screen]
        version = get_in(params, ["version"]) || params[:version]
        with {:ok, page} <- find_page_by_names(screen, version, page_name) do
          list_forms(%{page_id: page.id})
        end

      true ->
        {:error, "page_id or (screen, page, version) required"}
    end
  end

  def get_form(params) do
    cond do
      form_id = get_in(params, ["form_id"]) || params[:form_id] ->
        query = from f in FormV2Schema,
                where: f.id == ^form_id
        case Repo.one(query) do
          nil -> {:error, "Form not found"}
          form -> {:ok, form}
        end

      form_name = get_in(params, ["form"]) || params[:form] ->
        screen = get_in(params, ["screen"]) || params[:screen]
        page = get_in(params, ["page"]) || params[:page]
        version = get_in(params, ["version"]) || params[:version]
        find_form_by_names(screen, version, page, form_name)

      true ->
        {:error, "form_id or (screen, page, form, version) required"}
    end
  end

  def get_view_form(params) do
    cond do
      form_id = get_in(params, ["form_id"]) || params[:form_id] ->
        query = from f in FormV2Schema,
                where: f.id == ^form_id and f.app_view == true
        case Repo.one(query) do
          nil -> {:error, "Form not found"}
          form -> {:ok, form}
        end

      form_name = get_in(params, ["form"]) || params[:form] ->
        screen = get_in(params, ["screen"]) || params[:screen]
        page = get_in(params, ["page"]) || params[:page]
        version = get_in(params, ["version"]) || params[:version]
        find_form_by_names(screen, version, page, form_name)

      true ->
        {:error, "form_id or (screen, page, form, version) required"}
    end
  end

  def get_form_fields(form_id) do
    Logger.info("[get_form_fields] Starting field loading")
    Logger.info("[get_form_fields] Form ID: #{inspect(form_id)}")

    if form_id do
      # First check if there are ANY fields for this form (including inactive)
      total_fields_query = from ff in FormFieldV2Schema, where: ff.form_id == ^form_id
      total_fields_count = Repo.aggregate(total_fields_query, :count)
      Logger.info("[get_form_fields] Total fields in DB for form #{form_id}: #{total_fields_count}")

      # Check active fields count
      active_fields_query = from ff in FormFieldV2Schema, where: ff.form_id == ^form_id
      active_fields_count = Repo.aggregate(active_fields_query, :count)
      Logger.info("[get_form_fields] Active fields in DB for form #{form_id}: #{active_fields_count}")

      # Get sample of all fields for this form to see what's there
      if total_fields_count > 0 do
        sample_fields = Repo.all(from ff in FormFieldV2Schema, where: ff.form_id == ^form_id, limit: 3)
        Logger.info("[get_form_fields] Sample fields: #{inspect(sample_fields)}")
      end

      query =
        from ff in FormFieldV2Schema,
          where: ff.form_id == ^form_id,
          where: ff.active == true,
          order_by: [asc: ff.field_order, asc: ff.inserted_at]

      result = Repo.all(query)
      Logger.info("[get_form_fields] Query result count: #{length(result)}")
      Logger.info("[get_form_fields] Fields returned: #{inspect(result)}")

      {:ok, result}
    else
      Logger.info("[get_form_fields] Form ID is nil, returning empty list")
      # When form_id is nil, return empty list (for back button)
      {:ok, []}
    end
  end

  def structure(params \\ %{}) do
    version = get_in(params, ["version"]) || params[:version]

    with {:ok, screens} <- list_screens(%{version: version, active: true}) do
      result = Enum.map(screens, fn s ->
        pages = Repo.all(from p in PageSchema, where: p.screen_id == ^s.id and p.active == true, order_by: [asc: p.order])
        pages_with_forms = Enum.map(pages, fn p ->
          forms = Repo.all(from f in FormV2Schema, where: f.page_id == ^p.id and f.active == true and f.app_view == true, order_by: [asc: f.order])
          %{id: p.id, name: p.name, order: p.order, forms: Enum.map(forms, &%{id: &1.id, name: &1.name, order: &1.order, submit_to: &1.submit_to})}
        end)
        %{id: s.id, name: s.name, version: s.version, order: s.order, pages: pages_with_forms}
      end)

      {:ok, result}
    end
  end

  # ======
  #  CRUD
  # ======

  # Screens
  def create_screen(attrs), do: %ScreenSchema{} |> ScreenSchema.changeset(attrs) |> Repo.insert()
  def update_screen(id, attrs), do: Repo.get(ScreenSchema, id) |> maybe_update(ScreenSchema, attrs)
  def delete_screen(id), do: Repo.get(ScreenSchema, id) |> maybe_delete()

  # Pages
  def create_page(attrs), do: %PageSchema{} |> PageSchema.changeset(attrs) |> Repo.insert()
  def update_page(id, attrs), do: Repo.get(PageSchema, id) |> maybe_update(PageSchema, attrs)
  def delete_page(id), do: Repo.get(PageSchema, id) |> maybe_delete()

  # Forms
  def create_form(attrs), do: %FormV2Schema{} |> FormV2Schema.changeset(attrs) |> Repo.insert()
  def update_form(id, attrs), do: Repo.get(FormV2Schema, id) |> maybe_update(FormV2Schema, attrs)
  def delete_form(id), do: Repo.get(FormV2Schema, id) |> maybe_delete()

  # Fields
  def create_field(attrs), do: %FormFieldV2Schema{} |> FormFieldV2Schema.changeset(attrs) |> Repo.insert()
  def update_field(id, attrs), do: Repo.get(FormFieldV2Schema, id) |> maybe_update(FormFieldV2Schema, attrs)
  def delete_field(id), do: Repo.get(FormFieldV2Schema, id) |> maybe_delete()

  def reorder_fields(form_id, from_id, to_id) do
    # Get all fields for the form
    fields =
      from(f in FormFieldV2Schema,
        where: f.form_id == ^form_id,
        order_by: [asc: f.field_order]
      )
      |> Repo.all()

    # Find the from and to fields
    from_field = Enum.find(fields, &(&1.id == from_id))
    to_field = Enum.find(fields, &(&1.id == to_id))

    case {from_field, to_field} do
      {nil, _} -> {:error, "Source field not found"}
      {_, nil} -> {:error, "Target field not found"}
      {from_field, to_field} ->
        # Reorder the fields
        reordered_fields = reorder_list(fields, from_field, to_field)

        # Update field orders in database
        Repo.transaction(fn ->
          reordered_fields
          |> Enum.with_index()
          |> Enum.each(fn {field, index} ->
            Repo.update!(FormFieldV2Schema.changeset(field, %{"field_order" => index}))
          end)
        end)
    end
  end

  # Helper function to reorder a list by moving an item
  defp reorder_list(list, from_item, to_item) do
    # Remove the from_item from the list
    list_without_from = Enum.reject(list, &(&1.id == from_item.id))

    # Find the position to insert the from_item
    to_index = Enum.find_index(list_without_from, &(&1.id == to_item.id))

    case to_index do
      nil -> list # If to_item not found, return original list
      index ->
        # Insert the from_item at the target position
        {before, after_items} = Enum.split(list_without_from, index)
        before ++ [from_item] ++ after_items
    end
  end

  # =================
  # Helper functions
  # =================

  defp maybe_where(field, params_key, params, schema \\ ScreenSchema) do
    case get_in(params, [to_string(params_key)]) || params[params_key] do
      nil -> from s in schema
      val -> from s in schema, where: field(s, ^params_key) == ^val
    end
  end

  defp maybe_where_active(query, params) do
    case get_in(params, ["active"]) || params[:active] do
      nil -> query
      val when is_boolean(val) -> from s in query, where: s.active == ^val
      _ -> query
    end
  end

  defp find_screen(name, version) when is_binary(name) do
    query = from s in ScreenSchema,
      where: s.name == ^name,
      where: is_nil(^version) or s.version == ^version,
      limit: 1

    case Repo.one(query) do
      nil -> {:error, "Screen not found"}
      screen -> {:ok, screen}
    end
  end

  defp find_page_by_names(screen_name, version, page_name) do
    with {:ok, screen} <- find_screen(screen_name, version) do
      query = from p in PageSchema,
        where: p.screen_id == ^screen.id and p.name == ^page_name,
        limit: 1

      case Repo.one(query) do
        nil -> {:error, "Page not found"}
        page -> {:ok, page}
      end
    end
  end

  defp find_form_by_names(screen_name, version, page_name, form_name) do
    with {:ok, page} <- find_page_by_names(screen_name, version, page_name) do
      query = from f in FormV2Schema,
        where: f.page_id == ^page.id and f.name == ^form_name,
        limit: 1

      case Repo.one(query) do
        nil -> {:error, "Form not found"}
        form -> {:ok, form}
      end
    end
  end

  defp maybe_update(nil, _schema, _attrs), do: {:error, "Not found"}
  defp maybe_update(struct, schema, attrs) do
    Logger.info("[maybe_update] Starting update for #{schema}")
    Logger.info("[maybe_update] Current struct: #{inspect(struct)}")
    Logger.info("[maybe_update] Update attrs: #{inspect(attrs)}")

    changeset = struct |> schema.changeset(attrs)

    Logger.info("[maybe_update] Changeset valid?: #{changeset.valid?}")
    Logger.info("[maybe_update] Changeset errors: #{inspect(changeset.errors)}")
    Logger.info("[maybe_update] Changeset changes: #{inspect(changeset.changes)}")

    case Repo.update(changeset) do
      {:ok, updated_struct} ->
        Logger.info("[maybe_update] Update successful: #{inspect(updated_struct)}")
        {:ok, updated_struct}
      {:error, changeset} ->
        Logger.error("[maybe_update] Update failed")
        Logger.error("[maybe_update] Failed changeset: #{inspect(changeset)}")
        {:error, changeset}
    end
  end

  defp maybe_delete(nil), do: {:error, "Not found"}
  defp maybe_delete(struct), do: Repo.delete(struct)

  # ================
  #  REORDERING
  # ================

  def reorder_screens(item_id, new_position) do
    Repo.transaction(fn ->
      # Get all screens ordered by current order
      screens = Repo.all(from s in ScreenSchema, order_by: s.order)

      # Find the item to move
      item_to_move = Enum.find(screens, &(&1.id == item_id))
      if !item_to_move, do: Repo.rollback("Item not found")

      # Remove the item from its current position
      other_screens = Enum.reject(screens, &(&1.id == item_id))

      # Insert at new position
      {before, after_items} = Enum.split(other_screens, new_position)
      reordered_screens = before ++ [item_to_move] ++ after_items

      # Update order for all screens
      reordered_screens
      |> Enum.with_index()
      |> Enum.each(fn {screen, index} ->
        Repo.update!(ScreenSchema.changeset(screen, %{order: index}))
      end)

      :ok
    end)
  end

  def reorder_pages(item_id, new_position, screen_id) do
    Repo.transaction(fn ->
      # Get all pages for the screen ordered by current order
      pages = Repo.all(from p in PageSchema, where: p.screen_id == ^screen_id, order_by: p.order)

      # Find the item to move
      item_to_move = Enum.find(pages, &(&1.id == item_id))
      if !item_to_move, do: Repo.rollback("Item not found")

      # Remove the item from its current position
      other_pages = Enum.reject(pages, &(&1.id == item_id))

      # Insert at new position
      {before, after_items} = Enum.split(other_pages, new_position)
      reordered_pages = before ++ [item_to_move] ++ after_items

      # Update order for all pages
      reordered_pages
      |> Enum.with_index()
      |> Enum.each(fn {page, index} ->
        Repo.update!(PageSchema.changeset(page, %{order: index}))
      end)

      :ok
    end)
  end

  def reorder_forms(item_id, new_position, page_id) do
    Repo.transaction(fn ->
      # Get all forms for the page ordered by current order
      forms = Repo.all(from f in FormV2Schema, where: f.page_id == ^page_id, order_by: f.order)

      # Find the item to move
      item_to_move = Enum.find(forms, &(&1.id == item_id))
      if !item_to_move, do: Repo.rollback("Item not found")

      # Remove the item from its current position
      other_forms = Enum.reject(forms, &(&1.id == item_id))

      # Insert at new position
      {before, after_items} = Enum.split(other_forms, new_position)
      reordered_forms = before ++ [item_to_move] ++ after_items

      # Update order for all forms
      reordered_forms
      |> Enum.with_index()
      |> Enum.each(fn {form, index} ->
        Repo.update!(FormV2Schema.changeset(form, %{order: index}))
      end)

      :ok
    end)
  end
end
