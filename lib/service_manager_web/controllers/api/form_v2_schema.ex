defmodule ServiceManagerWeb.Api.FormV2Schema do
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, Ecto.UUID, autogenerate: true}
  @derive {Jason.Encoder, only: [:id, :page_id, :name, :order, :active, :submit_to, :route_id, :app_view, :value, :type]}

  schema "mobile_forms_v2" do
    field :name, :string
    field :order, :integer, default: 0
    field :active, :boolean, default: true
    field :submit_to, :string
    field :route_id, :string
    field :app_view, :boolean, default: true
    field :value, :string
    field :type, :string

    belongs_to :page, ServiceManagerWeb.Api.PageSchema, type: Ecto.UUID
    has_many :fields, ServiceManagerWeb.Api.FormFieldV2Schema, foreign_key: :form_id

    timestamps()
  end

  def changeset(form, attrs) do
    # Convert string boolean values to actual booleans
    normalized_attrs = normalize_boolean_attrs(attrs)
    
    form
    |> cast(normalized_attrs, [:name, :order, :active, :submit_to, :route_id, :page_id, :app_view, :value, :type])
    |> validate_required([:name, :page_id])
    |> validate_length(:name, min: 1, max: 100)
    |> validate_length(:submit_to, max: 500)
    |> validate_length(:route_id, max: 100)
    |> validate_number(:order, greater_than_or_equal_to: 0)
    |> validate_inclusion(:type, ["confirmation", "summary", "form"])
    |> foreign_key_constraint(:page_id)
    |> unique_constraint([:page_id, :name], name: :mobile_forms_v2_unique_page_name)
  end

  # Helper function to normalize boolean attributes from form submissions
  defp normalize_boolean_attrs(attrs) do
    attrs
    |> normalize_boolean_field("active")
    |> normalize_boolean_field("app_view")
  end

  defp normalize_boolean_field(attrs, field_name) do
    case Map.get(attrs, field_name) do
      "on" -> Map.put(attrs, field_name, true)
      "off" -> Map.put(attrs, field_name, false)
      "true" -> Map.put(attrs, field_name, true)
      "false" -> Map.put(attrs, field_name, false)
      true -> attrs
      false -> attrs
      nil -> Map.put(attrs, field_name, false)  # Default unchecked checkboxes to false
      _ -> attrs
    end
  end
end

